﻿using GMCadiomMeeting.Shared.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GMCadiomMeeting.Shared.ViewModels.Invitation
{
    public class InvitationResponse
    {
        public int Id { get; set; }
        public int MeetingId { get; set; }
        public string InvitationToken { get; set; } = string.Empty;
        public int SentByUserId { get; set; }
        public string? SentByUserName { get; set; }
        public int? InvitedUserId { get; set; }
        public string? InviteeEmail { get; set; }
        public string? InviteeName { get; set; }
        public InvitationStatus Status { get; set; }
        public InvitationMethod Method { get; set; }
        public ParticipantRole InvitedRole { get; set; }
        public string? PersonalMessage { get; set; }
        public DateTime SentAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public DateTime? RespondedAt { get; set; }
        public bool AllowJoinBeforeHost { get; set; }
        public string? SentByUser { get; set; }
        public string? InvitedUser { get; set; }
        public string? MeetingTitle { get; set; }
        public DateTime? MeetingScheduledStart { get; set; }
        public string? MeetingHost { get; set; }
    }
}
