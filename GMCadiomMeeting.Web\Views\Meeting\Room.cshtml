@using GMCadiomMeeting.Shared.ViewModels.Meeting
@using GMCadiomMeeting.Shared.ViewModels.Participant
@using GMCadiomMeeting.Shared.ViewModels.User
@model MeetingDto
@{
    ViewData["Title"] = $"Meeting: {Model.Title}";
    Layout = null; // Full-screen layout for meeting room
    var user = ViewBag.User as UserDto;
    var participant = ViewBag.Participant as MeetingParticipantDto;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #1a1a1a;
            color: white;
            overflow: hidden;
        }
        .meeting-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        .video-grid {
            flex: 1;
            display: grid;
            gap: 10px;
            padding: 10px;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            grid-auto-rows: minmax(200px, 1fr);
        }
        .video-tile {
            background: #2a2a2a;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
            .video-tile video {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
            .video-tile .participant-info {
                position: absolute;
                bottom: 10px;
                left: 10px;
                background: rgba(0,0,0,0.7);
                padding: 5px 10px;
                border-radius: 4px;
                font-size: 14px;
            }
            .video-tile .participant-status {
                position: absolute;
                top: 10px;
                right: 10px;
                display: flex;
                gap: 5px;
            }
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .controls-bar {
            background: #2a2a2a;
            padding: 15px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
        }
        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s;
        }
            .control-btn.active {
                background: #28a745;
                color: white;
            }
            .control-btn.inactive {
                background: #dc3545;
                color: white;
            }
            .control-btn.neutral {
                background: #6c757d;
                color: white;
            }
            .control-btn:hover {
                transform: scale(1.1);
            }
        .chat-panel {
            position: fixed;
            right: -400px;
            top: 0;
            width: 400px;
            height: 100vh;
            background: #2a2a2a;
            transition: right 0.3s;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }
            .chat-panel.open {
                right: 0;
            }
        .chat-header {
            padding: 15px;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
        }
        .chat-input {
            padding: 15px;
            border-top: 1px solid #444;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px;
            background: #3a3a3a;
            border-radius: 8px;
        }
        .message-sender {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 5px;
        }
        .participants-panel {
            position: fixed;
            right: -300px;
            top: 0;
            width: 300px;
            height: 100vh;
            background: #2a2a2a;
            transition: right 0.3s;
            z-index: 1000;
        }
            .participants-panel.open {
                right: 0;
            }
        .meeting-info {
            position: absolute;
            top: 15px;
            left: 15px;
            background: rgba(0,0,0,0.7);
            padding: 10px 15px;
            border-radius: 8px;
            z-index: 100;
        }
        .connection-status {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0,0,0,0.7);
            padding: 8px 12px;
            border-radius: 8px;
            z-index: 100;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #dc3545;
            animation: pulse 2s infinite;
        }
        .status-indicator.connected {
            background: #28a745;
            animation: none;
        }
        .status-indicator.connecting {
            background: #ffc107;
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="meeting-container">
        <!-- Meeting Info -->
        <div class="meeting-info">
            <div class="fw-bold">@Model.Title</div>
            <div class="small text-muted">Meeting ID: @Model.MeetingCode</div>
        </div>

        <!-- Connection Status -->
        <div class="connection-status" id="connectionStatus">
            <div class="status-indicator" id="statusIndicator"></div>
            <span class="small" id="statusText">Connecting...</span>
        </div>

        <!-- Video Grid -->
        <div class="video-grid" id="videoGrid">
            <!-- Local Video -->
            <div class="video-tile" id="participant-@user?.Id">
                <video id="localVideo" autoplay muted></video>
                <div class="participant-info">
                    @(user?.DisplayName ?? "You") (You)
                </div>
                <div class="participant-status">
                    <div class="status-icon bg-success" id="localCameraStatus">
                        <i class="fas fa-video"></i>
                    </div>
                    <div class="status-icon bg-success" id="localMicStatus">
                        <i class="fas fa-microphone"></i>
                    </div>
                </div>
            </div>

            <!-- Remote videos will be added dynamically -->
        </div>

        <!-- Controls Bar -->
        <div class="controls-bar">
            <button class="control-btn active" id="micBtn" title="Microphone">
                <i class="fas fa-microphone"></i>
            </button>
            <button class="control-btn active" id="cameraBtn" title="Camera">
                <i class="fas fa-video"></i>
            </button>
            <button class="control-btn neutral" id="shareBtn" title="Share Screen">
                <i class="fas fa-desktop"></i>
            </button>
            <button class="control-btn neutral" id="chatBtn" title="Chat">
                <i class="fas fa-comments"></i>
            </button>
            <button class="control-btn neutral" id="participantsBtn" title="Participants">
                <i class="fas fa-users"></i>
            </button>
            <button class="control-btn neutral" id="settingsBtn" title="Settings">
                <i class="fas fa-cog"></i>
            </button>
            <button class="control-btn inactive" id="leaveBtn" title="Leave Meeting">
                <i class="fas fa-phone-slash"></i>
            </button>
        </div>
    </div>

    <!-- Chat Panel -->
    <div class="chat-panel" id="chatPanel">
        <div class="chat-header">
            <h6 class="mb-0">Meeting Chat</h6>
            <button class="btn btn-sm btn-outline-light" onclick="toggleChat()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="chat-messages" id="chatMessages">
            <!-- Messages will be added dynamically -->
        </div>
        <div class="chat-input">
            <div class="input-group">
                <input type="text" class="form-control" id="messageInput" placeholder="Type a message...">
                <button class="btn btn-primary" onclick="sendMessage()">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Participants Panel -->
    <div class="participants-panel" id="participantsPanel">
        <div class="chat-header">
            <h6 class="mb-0">Participants (@Model.Participants.Count)</h6>
            <button class="btn btn-sm btn-outline-light" onclick="toggleParticipants()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-3" id="participantsList">
            @foreach (var p in Model.Participants)
            {
                <div class="d-flex align-items-center mb-3" id="<EMAIL>">
                    <div class="flex-shrink-0">
                        <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-semibold">@(p.DisplayNameInMeeting ?? p.User?.DisplayName)</div>
                        <div class="small text-muted">@p.Role</div>
                    </div>
                    <div class="flex-shrink-0">
                        @if (p.IsCameraEnabled)
                        {
                            <i class="fas fa-video text-success me-1"></i>
                        }
                        @if (p.IsMicrophoneEnabled)
                        {
                            <i class="fas fa-microphone text-success"></i>
                        }
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/7.0.0/signalr.min.js"></script>
    <script src="~/js/webrtc-manager.js"></script>

    <script>
        // Meeting room functionality
        let connection = null;
        let webrtcManager = null;
        let isCameraOn = @(participant?.IsCameraEnabled.ToString().ToLower() ?? "false");
        let isMicOn = @(participant?.IsMicrophoneEnabled.ToString().ToLower() ?? "false");

        const meetingId = @Model.Id;
        const userId = @(user?.Id ?? 0);
        const userName = '@(user?.DisplayName ?? "Guest")';

        // Initialize meeting room
        document.addEventListener('DOMContentLoaded', async function() {
            await initializeSignalR();
            await initializeWebRTC();
            setupControlHandlers();
        });

        async function initializeWebRTC() {
            try {
                // Create WebRTC manager
                webrtcManager = new WebRTCManager(connection, userId, userName);
                webrtcManager.setMeetingId(meetingId);

                // Initialize local media
                await webrtcManager.initializeLocalMedia(isCameraOn, isMicOn);

                updateControlStates();
                console.log('WebRTC initialized successfully');
            } catch (error) {
                console.error('Error initializing WebRTC:', error);
                showErrorMessage('Failed to access camera/microphone. Please check your permissions.');
            }
        }

        // Handle existing participants when joining
        function handleExistingParticipants(participants) {
            if (!webrtcManager || !participants) return;

            participants.forEach(participant => {
                if (participant.UserId !== userId) {
                    // Create peer connections for existing participants
                    webrtcManager.createPeerConnection(participant.UserId, participant.DisplayName, true);
                }
            });
        }

        async function initializeSignalR() {
            connection = new signalR.HubConnectionBuilder()
                .withUrl("https://localhost:7035/meetingHub", {
                    skipNegotiation: true,
                    transport: signalR.HttpTransportType.WebSockets
                })
                .configureLogging(signalR.LogLevel.Information)
                .withAutomaticReconnect([0, 2000, 10000, 30000]) // Retry after 0, 2, 10, 30 seconds
                .build();

            setupSignalREventHandlers();
            setupConnectionStateHandlers();

            try {
                updateConnectionStatus('connecting', 'Connecting...');
                await connection.start();
                await connection.invoke('JoinMeeting', meetingId, userId, userName);
                console.log('Connected to meeting hub');
                updateConnectionStatus('connected', 'Connected');
                showSuccessMessage('Connected to meeting successfully.');
            } catch (error) {
                console.error('Error connecting to meeting hub:', error);
                updateConnectionStatus('disconnected', 'Connection Failed');
                showErrorMessage('Failed to connect to meeting. Attempting to reconnect...');
                await attemptReconnection();
            }
        }

        function setupSignalREventHandlers() {
            // Event handlers
            connection.on('ParticipantJoined', function(participant) {
                console.log('Participant joined:', participant);
                try {
                    addParticipantToUI(participant);
                } catch (error) {
                    console.error('Error adding participant to UI:', error);
                }
            });

            connection.on('ParticipantLeft', function(participant) {
                console.log('Participant left:', participant);
                try {
                    removeParticipantFromUI(participant);
                } catch (error) {
                    console.error('Error removing participant from UI:', error);
                }
            });

            connection.on('ChatMessage', function(message) {
                try {
                    addChatMessage(message);
                } catch (error) {
                    console.error('Error adding chat message:', error);
                }
            });

            connection.on('ParticipantMediaStateChanged', function(data) {
                try {
                    updateParticipantMediaState(data);
                } catch (error) {
                    console.error('Error updating participant media state:', error);
                }
            });

            // Additional SignalR event handlers
            connection.on('ParticipantsList', function(participants) {
                console.log('Received participants list:', participants);
                try {
                    // Update the participants list with current participants
                    if (participants && Array.isArray(participants)) {
                        participants.forEach(participant => {
                            if (participant.UserId !== userId) { // Don't add ourselves
                                addParticipantToUI(participant);
                            }
                        });

                        // Initialize WebRTC connections for existing participants
                        handleExistingParticipants(participants);
                    }
                } catch (error) {
                    console.error('Error processing participants list:', error);
                }
            });

            connection.on('Error', function(errorMessage) {
                console.error('SignalR Error:', errorMessage);
                showErrorMessage(`Server error: ${errorMessage}`);
            });
        }

        function setupConnectionStateHandlers() {
            connection.onreconnecting((error) => {
                console.log('SignalR reconnecting...', error);
                updateConnectionStatus('connecting', 'Reconnecting...');
                showWarningMessage('Connection lost. Reconnecting...');
            });

            connection.onreconnected((connectionId) => {
                console.log('SignalR reconnected with connection ID:', connectionId);
                updateConnectionStatus('connected', 'Connected');
                showSuccessMessage('Reconnected to meeting successfully.');

                // Rejoin the meeting after reconnection
                connection.invoke('JoinMeeting', meetingId, userId, userName)
                    .catch(error => {
                        console.error('Error rejoining meeting after reconnection:', error);
                        updateConnectionStatus('disconnected', 'Connection Error');
                        showErrorMessage('Failed to rejoin meeting after reconnection.');
                    });
            });

            connection.onclose((error) => {
                console.log('SignalR connection closed:', error);
                updateConnectionStatus('disconnected', 'Disconnected');
                showErrorMessage('Connection to meeting lost. Please refresh the page.');
            });
        }

        function updateConnectionStatus(status, text) {
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');

            if (indicator && statusText) {
                // Remove all status classes
                indicator.classList.remove('connected', 'connecting', 'disconnected');

                // Add the current status class
                indicator.classList.add(status);
                statusText.textContent = text;
            }
        }

        function setupControlHandlers() {
            // Microphone toggle
            document.getElementById('micBtn').addEventListener('click', toggleMicrophone);

            // Camera toggle
            document.getElementById('cameraBtn').addEventListener('click', toggleCamera);

            // Screen share
            document.getElementById('shareBtn').addEventListener('click', toggleScreenShare);

            // Chat toggle
            document.getElementById('chatBtn').addEventListener('click', toggleChat);

            // Participants toggle
            document.getElementById('participantsBtn').addEventListener('click', toggleParticipants);

            // Leave meeting
            document.getElementById('leaveBtn').addEventListener('click', leaveMeeting);

            // Chat input
            document.getElementById('messageInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        }

        async function toggleMicrophone() {
            if (!webrtcManager) return;

            try {
                isMicOn = webrtcManager.toggleAudio();
                updateControlStates();
                await safeInvoke('UpdateMediaState', meetingId, isCameraOn, isMicOn);
            } catch (error) {
                console.error('Error updating microphone state:', error);
                showErrorMessage('Failed to update microphone state.');
                // Revert the change if the server update failed
                isMicOn = webrtcManager.toggleAudio();
                updateControlStates();
            }
        }

        async function toggleCamera() {
            if (!webrtcManager) return;

            try {
                isCameraOn = webrtcManager.toggleVideo();
                updateControlStates();
                await safeInvoke('UpdateMediaState', meetingId, isCameraOn, isMicOn);
            } catch (error) {
                console.error('Error updating camera state:', error);
                showErrorMessage('Failed to update camera state.');
                // Revert the change if the server update failed
                isCameraOn = webrtcManager.toggleVideo();
                updateControlStates();
            }
        }

        async function toggleScreenShare() {
            if (!webrtcManager) return;

            if (!webrtcManager.isScreenSharing) {
                try {
                    const success = await webrtcManager.startScreenShare();
                    if (success) {
                        document.getElementById('shareBtn').classList.add('active');
                        await safeInvoke('StartScreenSharing', meetingId, 'screen-' + Date.now(), 'fullscreen');
                    }
                } catch (error) {
                    console.error('Error starting screen sharing:', error);
                    showErrorMessage('Failed to start screen sharing.');
                }
            } else {
                stopScreenShare();
            }
        }

        async function stopScreenShare() {
            if (!webrtcManager) return;

            try {
                await webrtcManager.stopScreenShare();
                document.getElementById('shareBtn').classList.remove('active');
                await safeInvoke('StopScreenSharing', meetingId, 'screen-' + Date.now());
            } catch (error) {
                console.error('Error stopping screen sharing:', error);
                showErrorMessage('Failed to stop screen sharing.');
            }
        }

        function toggleChat() {
            const chatPanel = document.getElementById('chatPanel');
            chatPanel.classList.toggle('open');
        }

        function toggleParticipants() {
            const participantsPanel = document.getElementById('participantsPanel');
            participantsPanel.classList.toggle('open');
        }

        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();

            if (!message) {
                return;
            }

            if (!connection || connection.state !== signalR.HubConnectionState.Connected) {
                showErrorMessage('Not connected to the meeting. Attempting to reconnect...');
                await attemptReconnection();
                return;
            }

            try {
                await connection.invoke('SendChatMessage', meetingId, message, 'text', null);
                input.value = '';
                // Clear any previous error messages
                clearErrorMessage();
            } catch (error) {
                console.error('Error sending message:', error);
                showErrorMessage('Failed to send message. Attempting to reconnect...');

                // Attempt to reconnect and retry sending the message
                const reconnected = await attemptReconnection();
                if (reconnected) {
                    try {
                        await connection.invoke('SendChatMessage', meetingId, message, 'text', null);
                        input.value = '';
                        clearErrorMessage();
                        showSuccessMessage('Message sent successfully after reconnection.');
                    } catch (retryError) {
                        console.error('Error sending message after reconnection:', retryError);
                        showErrorMessage('Failed to send message. Please try again.');
                    }
                } else {
                    showErrorMessage('Unable to reconnect. Please refresh the page.');
                }
            }
        }

        function addParticipantToUI(participant) {
            console.log('Adding participant to UI:', participant);

            // Extract participant data - handle different data structures
            const participantId = participant.UserId || participant.Id || participant.ParticipantId;
            const displayName = participant.DisplayName || participant.DisplayNameInMeeting ||
                              (participant.User && participant.User.DisplayName) || 'Unknown Participant';
            const role = participant.Role || 'Attendee';
            const isCameraEnabled = participant.IsCameraEnabled || false;
            const isMicrophoneEnabled = participant.IsMicrophoneEnabled || false;

            // Check if participant already exists
            if (document.getElementById(`participant-${participantId}`)) {
                console.log('Participant already exists in UI, skipping add');
                return;
            }

            // Create video tile for the new participant
            const videoGrid = document.getElementById('videoGrid');
            const videoTile = document.createElement('div');
            videoTile.className = 'video-tile';
            videoTile.id = `participant-${participantId}`;

            videoTile.innerHTML = `
                <video id="video-${participantId}" autoplay></video>
                <div class="participant-info">
                    ${displayName}
                </div>
                <div class="participant-status">
                    <div class="status-icon ${isCameraEnabled ? 'bg-success' : 'bg-danger'}" id="camera-status-${participantId}">
                        <i class="fas ${isCameraEnabled ? 'fa-video' : 'fa-video-slash'}"></i>
                    </div>
                    <div class="status-icon ${isMicrophoneEnabled ? 'bg-success' : 'bg-danger'}" id="mic-status-${participantId}">
                        <i class="fas ${isMicrophoneEnabled ? 'fa-microphone' : 'fa-microphone-slash'}"></i>
                    </div>
                </div>
            `;

            videoGrid.appendChild(videoTile);

            // Add participant to participants list
            const participantsList = document.getElementById('participantsList');

            // Check if participant already exists in list
            if (document.getElementById(`participant-list-${participantId}`)) {
                console.log('Participant already exists in participants list, skipping add');
                return;
            }

            const participantItem = document.createElement('div');
            participantItem.className = 'd-flex align-items-center mb-3';
            participantItem.id = `participant-list-${participantId}`;

            participantItem.innerHTML = `
                <div class="flex-shrink-0">
                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
                <div class="flex-grow-1 ms-3">
                    <div class="fw-semibold">${displayName}</div>
                    <div class="small text-muted">${role}</div>
                </div>
                <div class="flex-shrink-0">
                    ${isCameraEnabled ? '<i class="fas fa-video text-success me-1"></i>' : ''}
                    ${isMicrophoneEnabled ? '<i class="fas fa-microphone text-success"></i>' : ''}
                </div>
            `;

            participantsList.appendChild(participantItem);

            // Update participants count
            updateParticipantsCount();
        }

        function removeParticipantFromUI(participant) {
            console.log('Removing participant from UI:', participant);

            // Extract participant ID - handle different data structures
            const participantId = participant.UserId || participant.Id || participant.ParticipantId;

            if (!participantId) {
                console.error('Cannot remove participant: no valid ID found', participant);
                return;
            }

            // Remove video tile
            const videoTile = document.getElementById(`participant-${participantId}`);
            if (videoTile) {
                videoTile.remove();
                console.log(`Removed video tile for participant ${participantId}`);
            } else {
                console.log(`Video tile for participant ${participantId} not found`);
            }

            // Remove from participants list
            const participantListItem = document.getElementById(`participant-list-${participantId}`);
            if (participantListItem) {
                participantListItem.remove();
                console.log(`Removed participant list item for participant ${participantId}`);
            } else {
                console.log(`Participant list item for participant ${participantId} not found`);
            }

            // Update participants count
            updateParticipantsCount();
        }

        function updateParticipantMediaState(data) {
            console.log('Updating participant media state:', data);

            // Extract participant data - handle different data structures
            const participantId = data.UserId || data.Id || data.ParticipantId;
            const isCameraEnabled = data.IsCameraEnabled !== undefined ? data.IsCameraEnabled : false;
            const isMicrophoneEnabled = data.IsMicrophoneEnabled !== undefined ? data.IsMicrophoneEnabled : false;

            if (!participantId) {
                console.error('Cannot update media state: no valid participant ID found', data);
                return;
            }

            // Update video tile status icons
            const cameraStatus = document.getElementById(`camera-status-${participantId}`);
            const micStatus = document.getElementById(`mic-status-${participantId}`);

            if (cameraStatus) {
                cameraStatus.className = `status-icon ${isCameraEnabled ? 'bg-success' : 'bg-danger'}`;
                cameraStatus.innerHTML = `<i class="fas ${isCameraEnabled ? 'fa-video' : 'fa-video-slash'}"></i>`;
                console.log(`Updated camera status for participant ${participantId}: ${isCameraEnabled}`);
            } else {
                console.log(`Camera status element not found for participant ${participantId}`);
            }

            if (micStatus) {
                micStatus.className = `status-icon ${isMicrophoneEnabled ? 'bg-success' : 'bg-danger'}`;
                micStatus.innerHTML = `<i class="fas ${isMicrophoneEnabled ? 'fa-microphone' : 'fa-microphone-slash'}"></i>`;
                console.log(`Updated microphone status for participant ${participantId}: ${isMicrophoneEnabled}`);
            } else {
                console.log(`Microphone status element not found for participant ${participantId}`);
            }

            // Update participants list
            const participantListItem = document.getElementById(`participant-list-${participantId}`);
            if (participantListItem) {
                const statusContainer = participantListItem.querySelector('.flex-shrink-0:last-child');
                if (statusContainer) {
                    statusContainer.innerHTML = `
                        ${isCameraEnabled ? '<i class="fas fa-video text-success me-1"></i>' : ''}
                        ${isMicrophoneEnabled ? '<i class="fas fa-microphone text-success"></i>' : ''}
                    `;
                    console.log(`Updated participant list status for participant ${participantId}`);
                } else {
                    console.log(`Status container not found in participant list for participant ${participantId}`);
                }
            } else {
                console.log(`Participant list item not found for participant ${participantId}`);
            }
        }

        function updateParticipantsCount() {
            const participantsList = document.getElementById('participantsList');
            const participantItems = participantsList.querySelectorAll('.d-flex.align-items-center.mb-3');
            const totalCount = participantItems.length + 1; // +1 for current user

            const participantsHeader = document.querySelector('.participants-panel .chat-header h6');
            if (participantsHeader) {
                participantsHeader.textContent = `Participants (${totalCount})`;
            }
        }

        function addChatMessage(message) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message';
            messageDiv.innerHTML = `
                <div class="message-sender">${message.SenderName}</div>
                <div>${message.Content}</div>
                <div class="small text-muted">${new Date(message.SentAt).toLocaleTimeString()}</div>
            `;
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function updateControlStates() {
            const micBtn = document.getElementById('micBtn');
            const cameraBtn = document.getElementById('cameraBtn');
            const localMicStatus = document.getElementById('localMicStatus');
            const localCameraStatus = document.getElementById('localCameraStatus');

            if (isMicOn) {
                micBtn.classList.remove('inactive');
                micBtn.classList.add('active');
                localMicStatus.innerHTML = '<i class="fas fa-microphone"></i>';
            } else {
                micBtn.classList.remove('active');
                micBtn.classList.add('inactive');
                localMicStatus.innerHTML = '<i class="fas fa-microphone-slash"></i>';
            }

            if (isCameraOn) {
                cameraBtn.classList.remove('inactive');
                cameraBtn.classList.add('active');
                localCameraStatus.innerHTML = '<i class="fas fa-video"></i>';
            } else {
                cameraBtn.classList.remove('active');
                cameraBtn.classList.add('inactive');
                localCameraStatus.innerHTML = '<i class="fas fa-video-slash"></i>';
            }
        }

        async function leaveMeeting() {
            if (confirm('Are you sure you want to leave the meeting?')) {
                try {
                    if (connection && connection.state === signalR.HubConnectionState.Connected) {
                        await connection.invoke('LeaveMeeting');
                        await connection.stop();
                    }
                } catch (error) {
                    console.error('Error leaving meeting:', error);
                    // Continue with cleanup even if server call fails
                }

                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                }

                window.location.href = '@Url.Action("Dashboard", "Home")';
            }
        }

        // Reconnection and error handling functions
        async function attemptReconnection(maxRetries = 5) {
            let retryCount = 0;
            const retryDelays = [1000, 2000, 5000, 10000, 15000]; // Progressive delays

            while (retryCount < maxRetries) {
                try {
                    console.log(`Reconnection attempt ${retryCount + 1}/${maxRetries}`);
                    updateConnectionStatus('connecting', `Reconnecting... (${retryCount + 1}/${maxRetries})`);

                    if (connection.state === signalR.HubConnectionState.Disconnected) {
                        await connection.start();
                        await connection.invoke('JoinMeeting', meetingId, userId, userName);
                        console.log('Reconnection successful');
                        updateConnectionStatus('connected', 'Connected');
                        showSuccessMessage('Reconnected to meeting successfully.');
                        return true;
                    } else if (connection.state === signalR.HubConnectionState.Connected) {
                        console.log('Already connected');
                        updateConnectionStatus('connected', 'Connected');
                        return true;
                    }
                } catch (error) {
                    console.error(`Reconnection attempt ${retryCount + 1} failed:`, error);
                    retryCount++;

                    if (retryCount < maxRetries) {
                        const delay = retryDelays[retryCount - 1] || 15000;
                        updateConnectionStatus('connecting', `Retrying in ${delay / 1000}s...`);
                        showWarningMessage(`Reconnection failed. Retrying in ${delay / 1000} seconds...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }

            console.error('All reconnection attempts failed');
            updateConnectionStatus('disconnected', 'Connection Failed');
            showErrorMessage('Unable to reconnect to meeting. Please refresh the page.');
            return false;
        }

        function showErrorMessage(message) {
            showNotification(message, 'error');
        }

        function showWarningMessage(message) {
            showNotification(message, 'warning');
        }

        function showSuccessMessage(message) {
            showNotification(message, 'success');
        }

        function clearErrorMessage() {
            const existingNotification = document.getElementById('connectionNotification');
            if (existingNotification) {
                existingNotification.remove();
            }
        }

        function showNotification(message, type = 'info') {
            // Remove existing notification
            clearErrorMessage();

            const notification = document.createElement('div');
            notification.id = 'connectionNotification';
            notification.className = `alert alert-${getBootstrapAlertClass(type)} alert-dismissible fade show`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 400px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            `;

            notification.innerHTML = `
                <i class="fas ${getIconClass(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

            document.body.appendChild(notification);

            // Auto-remove success messages after 3 seconds
            if (type === 'success') {
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.remove();
                    }
                }, 3000);
            }
        }

        function getBootstrapAlertClass(type) {
            switch (type) {
                case 'error': return 'danger';
                case 'warning': return 'warning';
                case 'success': return 'success';
                default: return 'info';
            }
        }

        function getIconClass(type) {
            switch (type) {
                case 'error': return 'fa-exclamation-triangle';
                case 'warning': return 'fa-exclamation-circle';
                case 'success': return 'fa-check-circle';
                default: return 'fa-info-circle';
            }
        }

        // Enhanced error handling for all SignalR operations
        async function safeInvoke(methodName, ...args) {
            if (!connection || connection.state !== signalR.HubConnectionState.Connected) {
                console.warn(`Cannot invoke ${methodName}: not connected`);
                const reconnected = await attemptReconnection();
                if (!reconnected) {
                    throw new Error('Unable to establish connection');
                }
            }

            try {
                return await connection.invoke(methodName, ...args);
            } catch (error) {
                console.error(`Error invoking ${methodName}:`, error);

                // If it's a connection error, try to reconnect
                if (error.message.includes('connection') || error.message.includes('Connection')) {
                    const reconnected = await attemptReconnection();
                    if (reconnected) {
                        return await connection.invoke(methodName, ...args);
                    }
                }
                throw error;
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (webrtcManager) {
                webrtcManager.cleanup();
            }
            if (connection) {
                connection.stop();
            }
        });
    </script>
</body>
</html>
