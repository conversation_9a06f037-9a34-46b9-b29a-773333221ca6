﻿using GMCadiomMeeting.Shared.Enums;

namespace GMCadiomMeeting.Shared.ViewModels.Invitation.JoinViaInvitation
{
    public class JoinViaInvitationResponse
    {
        public int MeetingId { get; set; }
        public string MeetingCode { get; set; } = string.Empty;
        public string MeetingTitle { get; set; } = string.Empty;
        public int ParticipantId { get; set; }
        public ParticipantRole Role { get; set; }
        public string? DisplayName { get; set; }
    }
}
