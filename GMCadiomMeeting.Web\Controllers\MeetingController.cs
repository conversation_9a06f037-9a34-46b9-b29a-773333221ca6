using GMCadiomMeeting.Shared.Enums;
using GMCadiomMeeting.Shared.ViewModels.Invitation.SendInvitation;
using GMCadiomMeeting.Shared.ViewModels.Meeting.CreateMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;
using GMCadiomMeeting.Web.Services;
using Microsoft.AspNetCore.Mvc;

namespace GMCadiomMeeting.Web.Controllers;

public class MeetingController : Controller
{
    private readonly IAuthService _authService;
    private readonly IApiService _apiService;
    private readonly ILogger<MeetingController> _logger;

    public MeetingController(IAuthService authService, IApiService apiService, ILogger<MeetingController> logger)
    {
        _authService = authService;
        _apiService = apiService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> Index()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var meetings = await _apiService.GetUserMeetingsAsync(userId.Value);
        return View(meetings);
    }

    [HttpGet]
    public IActionResult Create()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var request = new CreateMeetingRequest
        {
            ScheduledStartTime = DateTime.Now.AddHours(1),
            ScheduledEndTime = DateTime.Now.AddHours(2)
        };

        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(CreateMeetingRequest request)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        if (!ModelState.IsValid)
        {
            return View(request);
        }

        if (request.ScheduledStartTime >= request.ScheduledEndTime)
        {
            ModelState.AddModelError(nameof(request.ScheduledEndTime), "End time must be after start time.");
            return View(request);
        }

        if (request.ScheduledStartTime < DateTime.Now.AddMinutes(-5))
        {
            ModelState.AddModelError(nameof(request.ScheduledStartTime), "Cannot schedule meetings in the past.");
            return View(request);
        }

        var meeting = await _apiService.CreateMeetingAsync(request, userId.Value);

        if (meeting != null)
        {
            _logger.LogInformation("Meeting {MeetingId} created by user {UserId}", meeting.Id, userId.Value);
            TempData["SuccessMessage"] = "Meeting created successfully!";
            return RedirectToAction("Details", new { id = meeting.Id });
        }

        ModelState.AddModelError(string.Empty, "Failed to create meeting. Please try again.");
        return View(request);
    }

    [HttpGet]
    public async Task<IActionResult> Details(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        var isParticipant = meeting.HostUserId == userId ||
                           meeting.Participants.Any(p => p.UserId == userId);

        if (!isParticipant)
        {
            return Forbid();
        }

        return View(meeting);
    }

    [HttpGet]
    public IActionResult Join()
    {
        var model = new JoinMeetingRequest();
        return View(model);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Join(JoinMeetingRequest model)
    {
        if (!ModelState.IsValid)
        {
            return View(model);
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            // For guest users, we'll need to handle this differently
            // For now, redirect to login
            return RedirectToAction("Login", "Account");
        }

        model.UserId = userId.Value;
        var participant = await _apiService.JoinMeetingAsync(model);

        if (participant != null)
        {
            _logger.LogInformation("User {UserId} joined meeting {MeetingCode}", userId.Value, model.MeetingCode);
            return RedirectToAction("Room", new { id = participant.MeetingId });
        }

        ModelState.AddModelError(string.Empty, "Failed to join meeting. Please check the meeting code and try again.");
        return View(model);
    }

    [HttpGet]
    public async Task<IActionResult> Room(int id)
    {
        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        UserDto? user = null;
        MeetingParticipantDto? participant = null;

        if (_authService.IsAuthenticated())
        {
            // Authenticated user
            var userId = _authService.GetCurrentUserId();
            participant = meeting.Participants.FirstOrDefault(p => p.UserId == userId);

            if (participant == null)
            {
                return Forbid();
            }

            user = await _authService.GetCurrentUserAsync();
        }
        else
        {
            // Guest user - check session for guest info
            var guestMeetingId = HttpContext.Session.GetInt32("GuestMeetingId");
            var guestParticipantId = HttpContext.Session.GetInt32("GuestParticipantId");
            var guestDisplayName = HttpContext.Session.GetString("GuestDisplayName");

            if (guestMeetingId != id || !guestParticipantId.HasValue || string.IsNullOrEmpty(guestDisplayName))
            {
                // Guest session invalid, redirect to join page
                return RedirectToAction("Join");
            }

            // Find guest participant
            participant = meeting.Participants.FirstOrDefault(p => p.Id == guestParticipantId.Value);
            if (participant == null)
            {
                return Forbid();
            }

            // Create a temporary user object for guest
            user = new UserDto
            {
                Id = 0, // Guest user ID
                DisplayName = guestDisplayName,
                Email = "<EMAIL>"
            };
        }

        ViewBag.User = user;
        ViewBag.Participant = participant;

        return View(meeting);
    }

    [HttpGet]
    public async Task<IActionResult> Invitations(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        if (meeting.HostUserId != userId)
        {
            return Forbid();
        }

        var invitations = await _apiService.GetMeetingInvitationsAsync(id);

        ViewBag.Meeting = meeting;
        return View(invitations);
    }

    [HttpGet]
    public async Task<IActionResult> SendInvitation(int id)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var meeting = await _apiService.GetMeetingAsync(id);
        if (meeting == null)
        {
            return NotFound();
        }

        var userId = _authService.GetCurrentUserId();
        if (meeting.HostUserId != userId)
        {
            return Forbid();
        }

        var request = new SendInvitationRequest
        {
            MeetingId = id,
            ExpiresAt = meeting.ScheduledStartTime.AddHours(-1)
        };

        ViewBag.Meeting = meeting;
        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> SendInvitation(SendInvitationRequest request)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        if (!ModelState.IsValid)
        {
            var meeting = await _apiService.GetMeetingAsync(request.MeetingId);
            ViewBag.Meeting = meeting;
            return View(request);
        }

        var result = await _apiService.SendInvitationsAsync(request, userId.Value);

        if (result)
        {
            TempData["SuccessMessage"] = "Invitations sent successfully!";
            return RedirectToAction("Invitations", new { id = request.MeetingId });
        }

        ModelState.AddModelError(string.Empty, "Failed to send invitations. Please try again.");
        var meetingForView = await _apiService.GetMeetingAsync(request.MeetingId);
        ViewBag.Meeting = meetingForView;
        return View(request);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> RespondToInvitation(int invitationId, bool accept)
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var result = await _apiService.RespondToInvitationAsync(invitationId, accept);

        if (result)
        {
            var message = accept ? "Invitation accepted!" : "Invitation declined.";
            TempData["SuccessMessage"] = message;
        }
        else
        {
            TempData["ErrorMessage"] = "Failed to respond to invitation.";
        }

        return RedirectToAction("MyInvitations");
    }

    [HttpGet]
    public async Task<IActionResult> MyInvitations()
    {
        if (!_authService.IsAuthenticated())
        {
            return RedirectToAction("Login", "Account");
        }

        var userId = _authService.GetCurrentUserId();
        if (!userId.HasValue)
        {
            return RedirectToAction("Login", "Account");
        }

        var invitations = await _apiService.GetUserInvitationsAsync(userId.Value);
        return View(invitations);
    }

    /// <summary>
    /// Handle invitation links for external users
    /// </summary>
    [HttpGet("join/{token}")]
    public async Task<IActionResult> JoinByInvitation(string token)
    {
        try
        {
            // Get invitation details by token
            var invitation = await _apiService.GetInvitationByTokenAsync(token);

            if (invitation == null)
            {
                TempData["ErrorMessage"] = "Invalid or expired invitation link.";
                return RedirectToAction("Join");
            }

            // Check if invitation is expired
            if (invitation.ExpiresAt.HasValue && invitation.ExpiresAt < DateTime.UtcNow)
            {
                TempData["ErrorMessage"] = "This invitation has expired.";
                return RedirectToAction("Join");
            }

            // Get meeting details
            var meeting = await _apiService.GetMeetingAsync(invitation.MeetingId);
            if (meeting == null)
            {
                TempData["ErrorMessage"] = "Meeting not found.";
                return RedirectToAction("Join");
            }

            // Check if meeting is still active
            if (meeting.Status == MeetingStatus.Ended || meeting.Status == MeetingStatus.Cancelled)
            {
                TempData["ErrorMessage"] = "This meeting has ended or been cancelled.";
                return RedirectToAction("Join");
            }

            // Check if user is authenticated
            if (_authService.IsAuthenticated())
            {
                var userId = _authService.GetCurrentUserId();
                if (userId.HasValue)
                {
                    // For authenticated users, respond to invitation and join
                    var accepted = await _apiService.RespondToInvitationAsync(invitation.Id, true);
                    if (accepted)
                    {
                        return RedirectToAction("Room", new { id = meeting.Id });
                    }
                }
            }

            // For external/guest users, show guest join page
            ViewBag.Invitation = invitation;
            ViewBag.Meeting = meeting;
            ViewBag.InvitationToken = token;

            return View("JoinAsGuest");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing invitation link with token {Token}", token);
            TempData["ErrorMessage"] = "An error occurred while processing the invitation.";
            return RedirectToAction("Join");
        }
    }

    /// <summary>
    /// Handle guest user joining via invitation
    /// </summary>
    [HttpPost("join/{token}")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> JoinAsGuest(string token, string displayName, bool isCameraEnabled = false, bool isMicrophoneEnabled = false)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(displayName))
            {
                ModelState.AddModelError("displayName", "Display name is required.");

                // Reload invitation and meeting data for the view
                var invitation = await _apiService.GetInvitationByTokenAsync(token);
                var meeting = await _apiService.GetMeetingAsync(invitation?.MeetingId ?? 0);
                ViewBag.Invitation = invitation;
                ViewBag.Meeting = meeting;
                ViewBag.InvitationToken = token;

                return View("JoinAsGuest");
            }

            // Join meeting as guest via invitation token
            var joinResult = await _apiService.JoinViaInvitationAsync(token, displayName, isCameraEnabled, isMicrophoneEnabled);

            if (joinResult != null)
            {
                // Store guest session info
                HttpContext.Session.SetString("GuestDisplayName", displayName);
                HttpContext.Session.SetInt32("GuestParticipantId", joinResult.ParticipantId);
                HttpContext.Session.SetInt32("GuestMeetingId", joinResult.MeetingId);

                return RedirectToAction("Room", new { id = joinResult.MeetingId });
            }

            TempData["ErrorMessage"] = "Failed to join the meeting. Please try again.";
            return RedirectToAction("JoinByInvitation", new { token });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting as guest with token {Token}", token);
            TempData["ErrorMessage"] = "An error occurred while joining the meeting.";
            return RedirectToAction("JoinByInvitation", new { token });
        }
    }
}
