/**
 * WebRTC Manager for GMCadiom Meeting
 * Handles peer connections, media streams, and signaling
 */

class WebRTCManager {
    constructor(signalRConnection, localUserId, localUserName) {
        this.connection = signalRConnection;
        this.localUserId = localUserId;
        this.localUserName = localUserName;
        this.localStream = null;
        this.screenStream = null;
        this.peerConnections = new Map(); // userId -> RTCPeerConnection
        this.remoteStreams = new Map(); // userId -> MediaStream
        this.isScreenSharing = false;
        
        // WebRTC configuration
        this.rtcConfig = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' },
                { urls: 'stun:stun2.l.google.com:19302' }
            ]
        };

        this.setupSignalRHandlers();
    }

    /**
     * Initialize local media stream
     */
    async initializeLocalMedia(videoEnabled = true, audioEnabled = true) {
        try {
            this.localStream = await navigator.mediaDevices.getUserMedia({
                video: videoEnabled,
                audio: audioEnabled
            });

            // Display local video
            const localVideo = document.getElementById('localVideo');
            if (localVideo) {
                localVideo.srcObject = this.localStream;
            }

            console.log('Local media initialized');
            return this.localStream;
        } catch (error) {
            console.error('Error accessing media devices:', error);
            throw error;
        }
    }

    /**
     * Setup SignalR event handlers for WebRTC signaling
     */
    setupSignalRHandlers() {
        // Handle incoming WebRTC signals
        this.connection.on('ReceiveSignal', async (data) => {
            await this.handleSignal(data.FromUserId, data.Signal, data.SignalType);
        });

        // Handle participant joined
        this.connection.on('ParticipantJoined', async (data) => {
            if (data.UserId !== this.localUserId) {
                await this.createPeerConnection(data.UserId, data.DisplayName, true);
            }
        });

        // Handle participant left
        this.connection.on('ParticipantLeft', (data) => {
            this.removePeerConnection(data.UserId);
        });
    }

    /**
     * Create a peer connection for a remote user
     */
    async createPeerConnection(remoteUserId, remoteUserName, isInitiator = false) {
        if (this.peerConnections.has(remoteUserId)) {
            console.log(`Peer connection already exists for user ${remoteUserId}`);
            return;
        }

        console.log(`Creating peer connection for user ${remoteUserId}, initiator: ${isInitiator}`);

        const peerConnection = new RTCPeerConnection(this.rtcConfig);
        this.peerConnections.set(remoteUserId, peerConnection);

        // Add local stream tracks to peer connection
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => {
                peerConnection.addTrack(track, this.localStream);
            });
        }

        // Handle incoming remote stream
        peerConnection.ontrack = (event) => {
            console.log(`Received remote stream from user ${remoteUserId}`);
            const remoteStream = event.streams[0];
            this.remoteStreams.set(remoteUserId, remoteStream);
            this.displayRemoteVideo(remoteUserId, remoteUserName, remoteStream);
        };

        // Handle ICE candidates
        peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
                this.sendSignal(remoteUserId, event.candidate, 'ice-candidate');
            }
        };

        // Handle connection state changes
        peerConnection.onconnectionstatechange = () => {
            console.log(`Connection state for user ${remoteUserId}: ${peerConnection.connectionState}`);
            if (peerConnection.connectionState === 'failed') {
                this.removePeerConnection(remoteUserId);
            }
        };

        // If we're the initiator, create and send offer
        if (isInitiator) {
            try {
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                this.sendSignal(remoteUserId, offer, 'offer');
            } catch (error) {
                console.error(`Error creating offer for user ${remoteUserId}:`, error);
            }
        }

        return peerConnection;
    }

    /**
     * Handle incoming WebRTC signals
     */
    async handleSignal(fromUserId, signal, signalType) {
        console.log(`Received ${signalType} from user ${fromUserId}`);

        let peerConnection = this.peerConnections.get(fromUserId);

        try {
            switch (signalType) {
                case 'offer':
                    if (!peerConnection) {
                        peerConnection = await this.createPeerConnection(fromUserId, `User ${fromUserId}`, false);
                    }
                    await peerConnection.setRemoteDescription(new RTCSessionDescription(signal));
                    const answer = await peerConnection.createAnswer();
                    await peerConnection.setLocalDescription(answer);
                    this.sendSignal(fromUserId, answer, 'answer');
                    break;

                case 'answer':
                    if (peerConnection) {
                        await peerConnection.setRemoteDescription(new RTCSessionDescription(signal));
                    }
                    break;

                case 'ice-candidate':
                    if (peerConnection) {
                        await peerConnection.addIceCandidate(new RTCIceCandidate(signal));
                    }
                    break;

                default:
                    console.warn(`Unknown signal type: ${signalType}`);
            }
        } catch (error) {
            console.error(`Error handling ${signalType} from user ${fromUserId}:`, error);
        }
    }

    /**
     * Send WebRTC signal via SignalR
     */
    async sendSignal(targetUserId, signal, signalType) {
        try {
            await this.connection.invoke('SendSignal', this.meetingId, targetUserId, signal, signalType);
        } catch (error) {
            console.error(`Error sending ${signalType} to user ${targetUserId}:`, error);
        }
    }

    /**
     * Display remote video stream
     */
    displayRemoteVideo(userId, userName, stream) {
        const videoGrid = document.getElementById('videoGrid');
        if (!videoGrid) return;

        // Remove existing video element if it exists
        const existingElement = document.getElementById(`participant-${userId}`);
        if (existingElement) {
            existingElement.remove();
        }

        // Create new video element
        const videoTile = document.createElement('div');
        videoTile.className = 'video-tile';
        videoTile.id = `participant-${userId}`;

        const video = document.createElement('video');
        video.autoplay = true;
        video.playsInline = true;
        video.srcObject = stream;

        const participantInfo = document.createElement('div');
        participantInfo.className = 'participant-info';
        participantInfo.textContent = userName;

        videoTile.appendChild(video);
        videoTile.appendChild(participantInfo);
        videoGrid.appendChild(videoTile);
    }

    /**
     * Remove peer connection and clean up
     */
    removePeerConnection(userId) {
        const peerConnection = this.peerConnections.get(userId);
        if (peerConnection) {
            peerConnection.close();
            this.peerConnections.delete(userId);
        }

        this.remoteStreams.delete(userId);

        // Remove video element
        const videoElement = document.getElementById(`participant-${userId}`);
        if (videoElement) {
            videoElement.remove();
        }

        console.log(`Removed peer connection for user ${userId}`);
    }

    /**
     * Toggle local video
     */
    toggleVideo() {
        if (this.localStream) {
            const videoTrack = this.localStream.getVideoTracks()[0];
            if (videoTrack) {
                videoTrack.enabled = !videoTrack.enabled;
                return videoTrack.enabled;
            }
        }
        return false;
    }

    /**
     * Toggle local audio
     */
    toggleAudio() {
        if (this.localStream) {
            const audioTrack = this.localStream.getAudioTracks()[0];
            if (audioTrack) {
                audioTrack.enabled = !audioTrack.enabled;
                return audioTrack.enabled;
            }
        }
        return false;
    }

    /**
     * Start screen sharing
     */
    async startScreenShare() {
        try {
            this.screenStream = await navigator.mediaDevices.getDisplayMedia({ 
                video: true, 
                audio: true 
            });

            const videoTrack = this.screenStream.getVideoTracks()[0];
            
            // Replace video track in all peer connections
            for (const [userId, peerConnection] of this.peerConnections) {
                const sender = peerConnection.getSenders().find(s => 
                    s.track && s.track.kind === 'video'
                );
                if (sender) {
                    await sender.replaceTrack(videoTrack);
                }
            }

            // Update local video display
            const localVideo = document.getElementById('localVideo');
            if (localVideo) {
                localVideo.srcObject = this.screenStream;
            }

            this.isScreenSharing = true;

            // Handle screen share end
            videoTrack.onended = () => {
                this.stopScreenShare();
            };

            return true;
        } catch (error) {
            console.error('Error starting screen share:', error);
            return false;
        }
    }

    /**
     * Stop screen sharing
     */
    async stopScreenShare() {
        if (!this.isScreenSharing || !this.screenStream) return;

        // Stop screen stream
        this.screenStream.getTracks().forEach(track => track.stop());

        // Restore camera video
        if (this.localStream) {
            const videoTrack = this.localStream.getVideoTracks()[0];
            
            // Replace screen track with camera track in all peer connections
            for (const [userId, peerConnection] of this.peerConnections) {
                const sender = peerConnection.getSenders().find(s => 
                    s.track && s.track.kind === 'video'
                );
                if (sender && videoTrack) {
                    await sender.replaceTrack(videoTrack);
                }
            }

            // Update local video display
            const localVideo = document.getElementById('localVideo');
            if (localVideo) {
                localVideo.srcObject = this.localStream;
            }
        }

        this.screenStream = null;
        this.isScreenSharing = false;
    }

    /**
     * Set meeting ID (needed for signaling)
     */
    setMeetingId(meetingId) {
        this.meetingId = meetingId;
    }

    /**
     * Clean up all connections and streams
     */
    cleanup() {
        // Close all peer connections
        for (const [userId, peerConnection] of this.peerConnections) {
            peerConnection.close();
        }
        this.peerConnections.clear();
        this.remoteStreams.clear();

        // Stop local streams
        if (this.localStream) {
            this.localStream.getTracks().forEach(track => track.stop());
        }
        if (this.screenStream) {
            this.screenStream.getTracks().forEach(track => track.stop());
        }
    }
}

// Export for use in other scripts
window.WebRTCManager = WebRTCManager;
