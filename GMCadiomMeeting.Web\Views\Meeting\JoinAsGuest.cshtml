@{
    ViewData["Title"] = "Join Meeting";
    var invitation = ViewBag.Invitation as GMCadiomMeeting.Shared.ViewModels.Invitation.InvitationResponse;
    var meeting = ViewBag.Meeting as GMCadiomMeeting.Shared.ViewModels.Meeting.MeetingDto;
    var token = ViewBag.InvitationToken as string;
}

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
    <div class="row w-100">
        <div class="col-md-8 col-lg-6 mx-auto">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h2 class="mb-0">
                        <i class="fas fa-video me-2"></i>
                        Join Meeting
                    </h2>
                </div>
                <div class="card-body p-5">
                    @if (meeting != null)
                    {
                        <div class="text-center mb-4">
                            <h4 class="text-primary">@meeting.Title</h4>
                            @if (!string.IsNullOrEmpty(meeting.Description))
                            {
                                <p class="text-muted">@meeting.Description</p>
                            }
                            <div class="row text-center mt-3">
                                <div class="col-6">
                                    <small class="text-muted">Meeting Code</small>
                                    <div class="fw-bold">@meeting.MeetingCode</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">Scheduled Time</small>
                                    <div class="fw-bold">@meeting.ScheduledStartTime.ToString("MMM dd, yyyy HH:mm")</div>
                                </div>
                            </div>
                        </div>
                    }

                    @if (invitation != null && !string.IsNullOrEmpty(invitation.PersonalMessage))
                    {
                        <div class="alert alert-info">
                            <i class="fas fa-envelope me-2"></i>
                            <strong>Personal Message:</strong> @invitation.PersonalMessage
                        </div>
                    }

                    <form asp-action="JoinAsGuest" asp-route-token="@token" method="post" id="joinForm">
                        @Html.AntiForgeryToken()
                        
                        <div class="mb-4">
                            <label for="displayName" class="form-label">
                                <i class="fas fa-user me-2"></i>Your Name
                            </label>
                            <input type="text" 
                                   class="form-control form-control-lg" 
                                   id="displayName" 
                                   name="displayName" 
                                   placeholder="Enter your name" 
                                   value="@(invitation?.InviteeName ?? "")"
                                   required>
                            <span class="text-danger" data-valmsg-for="displayName" data-valmsg-replace="true"></span>
                        </div>

                        <!-- Media Preview Section -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <div id="videoPreview" style="position: relative; height: 200px; background: #000; border-radius: 8px; overflow: hidden;">
                                            <video id="previewVideo" autoplay muted style="width: 100%; height: 100%; object-fit: cover; display: none;"></video>
                                            <div id="videoPlaceholder" class="d-flex align-items-center justify-content-center h-100 text-white">
                                                <div>
                                                    <i class="fas fa-video-slash fa-3x mb-2"></i>
                                                    <div>Camera Off</div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-check mt-3">
                                            <input class="form-check-input" type="checkbox" id="cameraToggle" name="isCameraEnabled">
                                            <label class="form-check-label" for="cameraToggle">
                                                <i class="fas fa-video me-1"></i> Enable Camera
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <div id="audioIndicator" class="d-flex align-items-center justify-content-center" style="height: 200px;">
                                            <div>
                                                <i class="fas fa-microphone-slash fa-3x text-muted mb-2"></i>
                                                <div class="text-muted">Microphone Off</div>
                                            </div>
                                        </div>
                                        <div class="form-check mt-3">
                                            <input class="form-check-input" type="checkbox" id="microphoneToggle" name="isMicrophoneEnabled">
                                            <label class="form-check-label" for="microphoneToggle">
                                                <i class="fas fa-microphone me-1"></i> Enable Microphone
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Join Meeting
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <small class="text-muted">
                            By joining this meeting, you agree to our terms of service and privacy policy.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let videoStream = null;
        let audioStream = null;

        document.addEventListener('DOMContentLoaded', function() {
            const cameraToggle = document.getElementById('cameraToggle');
            const microphoneToggle = document.getElementById('microphoneToggle');
            const previewVideo = document.getElementById('previewVideo');
            const videoPlaceholder = document.getElementById('videoPlaceholder');
            const audioIndicator = document.getElementById('audioIndicator');

            // Camera toggle
            cameraToggle.addEventListener('change', async function() {
                if (this.checked) {
                    try {
                        videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
                        previewVideo.srcObject = videoStream;
                        previewVideo.style.display = 'block';
                        videoPlaceholder.style.display = 'none';
                    } catch (error) {
                        console.error('Error accessing camera:', error);
                        this.checked = false;
                        alert('Unable to access camera. Please check your permissions.');
                    }
                } else {
                    if (videoStream) {
                        videoStream.getTracks().forEach(track => track.stop());
                        videoStream = null;
                    }
                    previewVideo.style.display = 'none';
                    videoPlaceholder.style.display = 'flex';
                }
            });

            // Microphone toggle
            microphoneToggle.addEventListener('change', async function() {
                if (this.checked) {
                    try {
                        audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        audioIndicator.querySelector('i').className = 'fas fa-microphone fa-3x text-success mb-2';
                        audioIndicator.querySelector('div').textContent = 'Microphone On';
                        audioIndicator.querySelector('div').className = 'text-success';
                    } catch (error) {
                        console.error('Error accessing microphone:', error);
                        this.checked = false;
                        alert('Unable to access microphone. Please check your permissions.');
                    }
                } else {
                    if (audioStream) {
                        audioStream.getTracks().forEach(track => track.stop());
                        audioStream = null;
                    }
                    audioIndicator.querySelector('i').className = 'fas fa-microphone-slash fa-3x text-muted mb-2';
                    audioIndicator.querySelector('div').textContent = 'Microphone Off';
                    audioIndicator.querySelector('div').className = 'text-muted';
                }
            });

            // Cleanup on form submit
            document.getElementById('joinForm').addEventListener('submit', function() {
                // Keep streams alive for the meeting room
                // They will be cleaned up in the meeting room
            });

            // Cleanup on page unload
            window.addEventListener('beforeunload', function() {
                if (videoStream) {
                    videoStream.getTracks().forEach(track => track.stop());
                }
                if (audioStream) {
                    audioStream.getTracks().forEach(track => track.stop());
                }
            });
        });
    </script>
}
