using GMCadiomMeeting.Shared.Models;
using GMCadiomMeeting.Shared.ViewModels.Invitation.JoinViaInvitation;
using GMCadiomMeeting.Shared.ViewModels.Invitation.SendInvitation;
using GMCadiomMeeting.Shared.ViewModels.Meeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.CreateMeeting;
using GMCadiomMeeting.Shared.ViewModels.Meeting.JoinMeeting;
using GMCadiomMeeting.Shared.ViewModels.Participant;
using GMCadiomMeeting.Shared.ViewModels.User;
using GMCadiomMeeting.Shared.ViewModels.User.Login;
using GMCadiomMeeting.Shared.ViewModels.User.RegisterUser;
using Newtonsoft.Json;
using System.Text;

namespace GMCadiomMeeting.Web.Services;

public interface IApiService
{
    Task<LoginResponse?> LoginAsync(LoginRequest request);
    Task<UserDto?> RegisterAsync(RegisterUserRequest request);
    Task<UserDto?> GetUserAsync(int userId);
    Task<List<MeetingDto>> GetUserMeetingsAsync(int userId);
    Task<MeetingDto?> GetMeetingAsync(int meetingId);
    Task<MeetingDto?> CreateMeetingAsync(CreateMeetingRequest request, int hostUserId);
    Task<MeetingParticipantDto?> JoinMeetingAsync(JoinMeetingRequest model);
    Task<List<InvitationDto>> GetUserInvitationsAsync(int userId);
    Task<List<InvitationDto>> GetMeetingInvitationsAsync(int meetingId);
    Task<bool> SendInvitationsAsync(SendInvitationRequest request, int sentByUserId);
    Task<bool> RespondToInvitationAsync(int invitationId, bool accept);
    Task<InvitationDto?> GetInvitationByTokenAsync(string token);
    Task<JoinViaInvitationResponse?> JoinViaInvitationAsync(string token, string displayName, bool isCameraEnabled = false, bool isMicrophoneEnabled = false);
    Task<List<ChatMessageDto>> GetMeetingMessagesAsync(int meetingId, int userId, int page = 1, int pageSize = 50);
    Task<ChatMessageDto?> SendChatMessageAsync(int meetingId, int senderId, string content, int? recipientId = null);
}

public class ApiService : IApiService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<ApiService> _logger;
    private readonly IConfiguration _configuration;

    public ApiService(HttpClient httpClient, ILogger<ApiService> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;

        var apiBaseUrl = _configuration["ApiSettings:BaseUrl"] ?? "https://localhost:7000";
        _httpClient.BaseAddress = new Uri(apiBaseUrl);
        _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
    }

    public async Task<LoginResponse?> LoginAsync(LoginRequest request)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new { email = request.Email, password = request.Password });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/users/login", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<LoginResponse>(responseContent);
            }

            _logger.LogWarning("Login failed for user {Email}. Status: {StatusCode}", request.Email, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during login for user {Email}", request.Email);
            return null;
        }
    }

    public async Task<UserDto?> RegisterAsync(RegisterUserRequest request)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                email = request.Email,
                password = request.Password,
                displayName = request.DisplayName,
                firstName = request.FirstName,
                lastName = request.LastName,
                timeZone = request.TimeZone
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/users/register", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<UserDto>(responseContent);
            }

            _logger.LogWarning("Registration failed for user {Email}. Status: {StatusCode}", request.Email, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during registration for user {Email}", request.Email);
            return null;
        }
    }

    public async Task<UserDto?> GetUserAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/users/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<UserDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {UserId}", userId);
            return null;
        }
    }

    public async Task<List<MeetingDto>> GetUserMeetingsAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/meetings/user/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<MeetingDto>>(responseContent) ?? new List<MeetingDto>();
            }

            return new List<MeetingDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meetings for user {UserId}", userId);
            return new List<MeetingDto>();
        }
    }

    public async Task<MeetingDto?> GetMeetingAsync(int meetingId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/meetings/{meetingId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting meeting {MeetingId}", meetingId);
            return null;
        }
    }

    public async Task<MeetingDto?> CreateMeetingAsync(CreateMeetingRequest request, int hostUserId)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                title = request.Title,
                description = request.Description,
                type = (int)request.Type,
                scheduledStartTime = request.ScheduledStartTime,
                scheduledEndTime = request.ScheduledEndTime,
                hostUserId = hostUserId,
                maxParticipants = request.MaxParticipants,
                password = request.Password,
                isRecordingEnabled = request.IsRecordingEnabled,
                isRecurring = request.IsRecurring,
                recurrencePattern = request.RecurrencePattern
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/meetings", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingDto>(responseContent);
            }

            _logger.LogWarning("Meeting creation failed. Status: {StatusCode}", response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating meeting");
            return null;
        }
    }

    public async Task<MeetingParticipantDto?> JoinMeetingAsync(JoinMeetingRequest request)
    {
        try
        {
            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"/api/meetings/{request.MeetingCode}/join", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<MeetingParticipantDto>(responseContent);
            }

            _logger.LogWarning("Join meeting failed for code {MeetingCode}. Status: {StatusCode}", request.MeetingCode, response.StatusCode);
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting {MeetingCode}", request.MeetingCode);
            return null;
        }
    }

    public async Task<List<InvitationDto>> GetUserInvitationsAsync(int userId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/user/{userId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<InvitationDto>>(responseContent) ?? new List<InvitationDto>();
            }

            return new List<InvitationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitations for user {UserId}", userId);
            return new List<InvitationDto>();
        }
    }

    public async Task<List<InvitationDto>> GetMeetingInvitationsAsync(int meetingId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/meeting/{meetingId}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<InvitationDto>>(responseContent) ?? new List<InvitationDto>();
            }

            return new List<InvitationDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitations for meeting {MeetingId}", meetingId);
            return new List<InvitationDto>();
        }
    }

    public async Task<bool> SendInvitationsAsync(SendInvitationRequest request, int sentByUserId)
    {
        try
        {
            var emails = request.EmailAddresse.Split('\n', StringSplitOptions.RemoveEmptyEntries)
                .Select(email => email.Trim())
                .Where(email => !string.IsNullOrEmpty(email))
                .ToList();

            var invitees = emails.Select(email => new
            {
                email = email,
                role = (int)request.InvitedRole
            }).ToList();

            var json = JsonConvert.SerializeObject(new
            {
                meetingId = request.MeetingId,
                sentByUserId = sentByUserId,
                invitees = invitees,
                personalMessage = request.PersonalMessage,
                expiresAt = request.ExpiresAt,
                allowJoinBeforeHost = request.AllowJoinBeforeHost
            });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/invitations/send", content);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending invitations for meeting {MeetingId}", request.MeetingId);
            return false;
        }
    }

    public async Task<bool> RespondToInvitationAsync(int invitationId, bool accept)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new { accept = accept });
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"/api/invitations/{invitationId}/respond", content);

            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error responding to invitation {InvitationId}", invitationId);
            return false;
        }
    }

    public async Task<List<ChatMessageDto>> GetMeetingMessagesAsync(int meetingId, int userId, int page = 1, int pageSize = 50)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/chat/meeting/{meetingId}?userId={userId}&page={page}&pageSize={pageSize}");

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<List<ChatMessageDto>>(responseContent) ?? new List<ChatMessageDto>();
            }

            return new List<ChatMessageDto>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting messages for meeting {MeetingId}", meetingId);
            return new List<ChatMessageDto>();
        }
    }

    public async Task<ChatMessageDto?> SendChatMessageAsync(int meetingId, int senderId, string content, int? recipientId = null)
    {
        try
        {
            var json = JsonConvert.SerializeObject(new
            {
                meetingId = meetingId,
                senderId = senderId,
                recipientId = recipientId,
                content = content,
                type = 0, // Text
                scope = recipientId.HasValue ? 1 : 0 // Private if recipient, otherwise Public
            });
            var contentObj = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/chat/send", contentObj);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<ChatMessageDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat message in meeting {MeetingId}", meetingId);
            return null;
        }
    }

    public async Task<InvitationDto?> GetInvitationByTokenAsync(string token)
    {
        try
        {
            var response = await _httpClient.GetAsync($"/api/invitations/token/{token}");
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<InvitationDto>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting invitation by token {Token}", token);
            return null;
        }
    }

    public async Task<JoinViaInvitationResponse?> JoinViaInvitationAsync(string token, string displayName, bool isCameraEnabled = false, bool isMicrophoneEnabled = false)
    {
        try
        {
            var request = new JoinViaInvitationRequest
            {
                DisplayName = displayName,
                IsCameraEnabled = isCameraEnabled,
                IsMicrophoneEnabled = isMicrophoneEnabled
            };

            var json = JsonConvert.SerializeObject(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"/api/invitations/join/{token}", content);
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                return JsonConvert.DeserializeObject<JoinViaInvitationResponse>(responseContent);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error joining meeting via invitation token {Token}", token);
            return null;
        }
    }
}
